{"name": "MemoryAgent", "displayName": "记忆三元组Agent", "version": "1.0.0", "description": "支持三元组知识图谱的回忆与查询。", "author": "Naga记忆模块", "agentType": "mcp", "entryPoint": {"module": "mcpserver.agent_memory.agent_memory", "class": "MemoryAgent"}, "factory": {"create_instance": "create_memory_agent"}, "communication": {"protocol": "stdio", "timeout": 15000}, "capabilities": {"invocationCommands": [{"command": "recall", "description": "根据用户问题查询知识图谱三元组，返回相关记忆内容。\n- `tool_name`: 固定为 `recall`\n- `query`: 查询内容（必需）\n**调用示例:**\n```json\n{\"tool_name\": \"recall\", \"query\": \"西藏的雪山\"}\n```", "example": "{\"tool_name\": \"recall\", \"query\": \"西藏的雪山\"}"}]}, "inputSchema": {"type": "object", "properties": {"tool_name": {"type": "string", "description": "工具名称：recall"}, "query": {"type": "string", "description": "查询内容（必需）"}}, "required": ["tool_name", "query"]}, "outputSchema": {"type": "object", "properties": {"status": {"type": "string", "description": "状态：ok/error"}, "message": {"type": "string", "description": "消息描述"}, "data": {"type": "string", "description": "查询结果内容"}}}, "runtime": {"instance": null, "is_initialized": false, "last_used": null, "usage_count": 0}}