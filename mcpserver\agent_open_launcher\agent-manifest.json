{"name": "AppLauncherAgent", "displayName": "智能应用启动Agent", "version": "1.0.0", "description": "支持启动电脑应用：打开应用、列出应用、刷新应用列表。", "author": "Naga应用操作模块", "agentType": "mcp", "entryPoint": {"module": "mcpserver.agent_open_launcher.agent_app_launcher", "class": "AppLauncherAgent"}, "factory": {"create_instance": "create_app_launcher_agent", "validate_config": "validate_agent_config", "get_dependencies": "get_agent_dependencies"}, "communication": {"protocol": "stdio", "timeout": 15000}, "capabilities": {"invocationCommands": [{"command": "open", "description": "启动指定应用。\n- `tool_name`: 固定为 `open`\n- `app`: 应用名称或路径\n- `args`: 启动参数（可选）\n**调用示例:**\n```json\n{\"tool_name\": \"open\", \"app\": \"notepad\"}```", "example": "{\"tool_name\": \"open\", \"app\": \"notepad\"}"}, {"command": "list", "description": "列出所有可用应用。\n- `tool_name`: 固定为 `list`\n**调用示例:**\n```json\n{\"tool_name\": \"list\"}```", "example": "{\"tool_name\": \"list\"}"}, {"command": "refresh", "description": "刷新应用列表缓存。\n- `tool_name`: 固定为 `refresh`\n**调用示例:**\n```json\n{\"tool_name\": \"refresh\"}```", "example": "{\"tool_name\": \"refresh\"}"}]}, "inputSchema": {"type": "object", "properties": {"tool_name": {"type": "string", "description": "工具名称：open/list/refresh"}, "app": {"type": "string", "description": "应用名称或路径（open时必需）"}, "args": {"type": "string", "description": "启动参数（open时可选）"}}, "required": ["tool_name"]}, "configSchema": {"APP_LAUNCHER_AGENT_CACHE_SIZE": "integer"}, "runtime": {"instance": null, "is_initialized": false, "last_used": null, "usage_count": 0}}